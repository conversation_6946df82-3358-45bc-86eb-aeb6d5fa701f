using FluentValidation;

namespace ReviewYourTipster.Application.Common.Validators;

public class SortableValidator : AbstractValidator<ISortable>
{
    public SortableValidator()
    {
        RuleFor(x => x.SortBy)
            .NotEmpty().WithErrorCode("SortBy.Required").WithMessage("SortBy is required");

        RuleFor(x => x.SortDirection)
            .IsInEnum().WithErrorCode("SortDirection.Invalid")
            .WithMessage("SortDirection must be 0 - Ascending or 1 - Descending");
    }
}