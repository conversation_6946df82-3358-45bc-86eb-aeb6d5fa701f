using FluentValidation;

namespace ReviewYourTipster.Application.Common.Validators;

public class PaginableValidator : AbstractValidator<IPaginable>
{
    public PaginableValidator()
    {
        RuleFor(x => x.Page)
            .NotEmpty().WithErrorCode("Page.Required").WithMessage("Page is required")
            .GreaterThanOrEqualTo(1).WithErrorCode("Page.OutOfRange")
            .WithMessage("Page must be greater than or equal to 1");

        RuleFor(x => x.PageSize)
            .NotEmpty().WithErrorCode("PageSize.Required").WithMessage("PageSize is required")
            .LessThanOrEqualTo(100).WithErrorCode("PageSize.OutOfRange")
            .WithMessage("PageSize must be less than or equal to 100");
    }
}