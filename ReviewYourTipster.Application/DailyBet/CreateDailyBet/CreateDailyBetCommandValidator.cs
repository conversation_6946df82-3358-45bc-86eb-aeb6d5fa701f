using FluentValidation;

namespace ReviewYourTipster.Application.DailyBet.CreateDailyBet;

public class CreateDailyBetCommandValidator : AbstractValidator<CreateDailyBetCommand>
{
    public CreateDailyBetCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required")
            .WithErrorCode("Name.Required")
            .MinimumLength(3)
            .WithMessage("Name must be at least 3 characters long")
            .WithErrorCode("Name.TooShort")
            .MaximumLength(255)
            .WithMessage("Name must not exceed 255 characters")
            .WithErrorCode("Name.TooLong");

        RuleFor(x => x.FirstCompetitor)
            .NotEmpty()
            .WithMessage("First competitor is required")
            .WithErrorCode("FirstCompetitor.Required")
            .MinimumLength(3)
            .WithMessage("First competitor must be at least 3 characters long")
            .WithErrorCode("FirstCompetitor.TooShort")
            .MaximumLength(255)
            .WithMessage("First competitor must not exceed 255 characters")
            .WithErrorCode("FirstCompetitor.TooLong");

        RuleFor(x => x.SecondCompetitor)
            .NotEmpty()
            .WithMessage("Second competitor is required")
            .WithErrorCode("SecondCompetitor.Required")
            .MinimumLength(3)
            .WithMessage("Second competitor must be at least 3 characters long")
            .WithErrorCode("SecondCompetitor.TooShort")
            .MaximumLength(255)
            .WithMessage("Second competitor must not exceed 255 characters")
            .WithErrorCode("SecondCompetitor.TooLong");

        RuleFor(x => x.MatchDate)
            .GreaterThan(DateTime.UtcNow)
            .WithMessage("Match date must be in the future")
            .WithErrorCode("MatchDate.Future");

        RuleFor(x => x.ImageUrl)
            .NotEmpty()
            .WithMessage("Image URL is required")
            .WithErrorCode("ImageUrl.Required")
            .Must(x => x != null && x.StartsWith("https://"))
            .WithMessage("Image URL must be a secure HTTPS URL")
            .WithErrorCode("ImageUrl.InvalidFormat")
            .MaximumLength(500)
            .WithMessage("Image URL must not exceed 500 characters")
            .WithErrorCode("ImageUrl.TooLong");

        RuleFor(x => x.BetUrl)
            .NotEmpty()
            .WithMessage("Bet URL is required")
            .WithErrorCode("BetUrl.Required")
            .Must(x => x != null && x.StartsWith("https://"))
            .WithMessage("Bet URL must be a secure HTTPS URL")
            .WithErrorCode("BetUrl.InvalidFormat")
            .MaximumLength(500)
            .WithMessage("Bet URL must not exceed 500 characters")
            .WithErrorCode("BetUrl.TooLong");
    }
}