using FluentValidation.TestHelper;
using ReviewYourTipster.Application.Common;
using ReviewYourTipster.Application.Common.Validators;

namespace ReviewYourTipster.ApplicationTests.Common.Validators;

public class PaginableValidatorTests
{
    private readonly PaginableValidator _validator = new();

    [Fact]
    public void Validate_ReturnsRequiredError_WhenPageIsZero()
    {
        // Arrange
        var paginable = new TestPaginable { Page = 0, PageSize = 10 };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Page)
            .WithErrorMessage("Page is required")
            .WithErrorCode("PageRequired");
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(-10)]
    [InlineData(-100)]
    public void Validate_ReturnsOutOfRangeError_WhenPageIsNegative(int page)
    {
        // Arrange
        var paginable = new TestPaginable { Page = page, PageSize = 10 };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Page)
            .WithErrorMessage("Page must be greater than or equal to 1")
            .WithErrorCode("PageOutOfRange");
    }

    [Theory]
    [InlineData(1)]
    [InlineData(5)]
    [InlineData(10)]
    [InlineData(100)]
    [InlineData(1000)]
    public void Validate_PassesValidation_WhenPageIsValid(int page)
    {
        // Arrange
        var paginable = new TestPaginable { Page = page, PageSize = 10 };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Page);
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenPageSizeIsZero()
    {
        // Arrange
        var paginable = new TestPaginable { Page = 1, PageSize = 0 };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PageSize)
            .WithErrorMessage("PageSize is required")
            .WithErrorCode("PageSizeRequired");
    }

    [Theory]
    [InlineData(101)]
    [InlineData(150)]
    [InlineData(200)]
    [InlineData(1000)]
    public void Validate_ReturnsOutOfRangeError_WhenPageSizeIsGreaterThan100(int pageSize)
    {
        // Arrange
        var paginable = new TestPaginable { Page = 1, PageSize = pageSize };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PageSize)
            .WithErrorMessage("PageSize must be less than or equal to 100")
            .WithErrorCode("PageSizeOutOfRange");
    }

    [Theory]
    [InlineData(1)]
    [InlineData(10)]
    [InlineData(25)]
    [InlineData(50)]
    [InlineData(100)]
    public void Validate_PassesValidation_WhenPageSizeIsValid(int pageSize)
    {
        // Arrange
        var paginable = new TestPaginable { Page = 1, PageSize = pageSize };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.PageSize);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(-10)]
    [InlineData(-100)]
    public void Validate_PassesValidation_WhenPageSizeIsNegative(int pageSize)
    {
        // Arrange
        var paginable = new TestPaginable { Page = 1, PageSize = pageSize };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.PageSize);
    }

    [Fact]
    public void Validate_PassesValidation_WhenBothPageAndPageSizeAreValid()
    {
        // Arrange
        var paginable = new TestPaginable { Page = 1, PageSize = 10 };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Validate_ReturnsMultipleErrors_WhenBothPageAndPageSizeAreInvalid()
    {
        // Arrange
        var paginable = new TestPaginable { Page = 0, PageSize = 101 };

        // Act
        var result = _validator.TestValidate(paginable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Page)
            .WithErrorCode("PageRequired");
        result.ShouldHaveValidationErrorFor(x => x.PageSize)
            .WithErrorCode("PageSizeOutOfRange");
    }

    /// <summary>
    /// Test implementation of IPaginable for testing purposes
    /// </summary>
    private class TestPaginable : IPaginable
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
    }
}