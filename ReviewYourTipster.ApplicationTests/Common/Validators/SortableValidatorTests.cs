using FluentValidation.TestHelper;
using ReviewYourTipster.Application.Common;
using ReviewYourTipster.Application.Common.Validators;

namespace ReviewYourTipster.ApplicationTests.Common.Validators;

public class SortableValidatorTests
{
    private readonly SortableValidator _validator = new();

    [Fact]
    public void Validate_ReturnsRequiredError_WhenSortByIsNull()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = null!, SortDirection = SortDirection.Ascending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SortBy)
            .WithErrorMessage("SortBy is required")
            .WithErrorCode("sortByRequired");
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenSortByIsEmpty()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = string.Empty, SortDirection = SortDirection.Ascending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SortBy)
            .WithErrorMessage("SortBy is required")
            .WithErrorCode("sortByRequired");
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenSortByIsWhitespace()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = "   ", SortDirection = SortDirection.Ascending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SortBy)
            .WithErrorMessage("SortBy is required")
            .WithErrorCode("sortByRequired");
    }

    [Theory]
    [InlineData("Name")]
    [InlineData("CreatedDate")]
    [InlineData("UpdatedDate")]
    [InlineData("Id")]
    public void Validate_PassesValidation_WhenSortByIsValid(string sortBy)
    {
        // Arrange
        var sortable = new TestSortable { SortBy = sortBy, SortDirection = SortDirection.Ascending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.SortBy);
    }

    [Fact]
    public void Validate_ReturnsInvalidEnumError_WhenSortDirectionIsInvalid()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = "Name", SortDirection = (SortDirection)999 };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SortDirection)
            .WithErrorMessage("SortDirection must be 0 - Ascending or 1 - Descending")
            .WithErrorCode("SortDirectionInvalid");
    }

    [Fact]
    public void Validate_PassesValidation_WhenSortDirectionIsAscending()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = "Name", SortDirection = SortDirection.Ascending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.SortDirection);
    }

    [Fact]
    public void Validate_PassesValidation_WhenSortDirectionIsDescending()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = "Name", SortDirection = SortDirection.Descending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.SortDirection);
    }

    [Fact]
    public void Validate_PassesValidation_WhenAllPropertiesAreValid()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = "Name", SortDirection = SortDirection.Ascending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Validate_ReturnsMultipleErrors_WhenMultiplePropertiesAreInvalid()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = string.Empty, SortDirection = (SortDirection)999 };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SortBy)
            .WithErrorCode("sortByRequired");
        result.ShouldHaveValidationErrorFor(x => x.SortDirection)
            .WithErrorCode("SortDirectionInvalid");
    }

    [Fact]
    public void Validate_PassesValidation_WhenSortByIsValidAndSortDirectionIsDescending()
    {
        // Arrange
        var sortable = new TestSortable { SortBy = "Name", SortDirection = SortDirection.Descending };

        // Act
        var result = _validator.TestValidate(sortable);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    private class TestSortable : ISortable
    {
        public string SortBy { get; set; } = string.Empty;
        public SortDirection SortDirection { get; set; }
    }
}